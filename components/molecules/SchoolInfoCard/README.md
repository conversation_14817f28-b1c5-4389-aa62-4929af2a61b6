# SchoolInfoCard Component

A reusable React component that displays school information in a card format using DaisyUI styling.

## Overview

The `SchoolInfoCard` is a molecule component that renders school details within a clean, responsive card structure. It includes an edit action button and handles optional fields gracefully.

## Features

- **DaisyUI Styling**: Uses DaisyUI card components for consistent design
- **Responsive Design**: Adapts to different screen sizes (mobile, tablet, desktop)
- **Optional Fields**: Gracefully handles missing or empty school data
- **TypeScript Support**: Fully typed with proper interfaces
- **Customizable**: Accepts custom className for additional styling
- **Accessible**: Includes proper ARIA labels and semantic HTML

## Props

### SchoolInfoCardProps

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `school` | `ISchoolResponse` | Yes | School data object containing all school information |
| `onEdit` | `(schoolId: string) => void` | Yes | Callback function triggered when edit button is clicked |
| `className` | `string` | No | Additional CSS classes for custom styling |

### ISchoolResponse Interface

The component uses the `ISchoolResponse` interface from `@/apis/schoolApi`:

```typescript
interface ISchoolResponse {
  id: string;
  name: string;
  address: string;
  phoneNumber: string;
  registeredNumber: string;
  email: string;
  admin?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  brand?: {
    id: string;
    logo?: string;
    color?: string;
    image?: string;
  };
  createdAt?: string;
  updatedAt?: string;
}
```

## Usage

### Basic Usage

```tsx
import { SchoolInfoCard } from '@/components/molecules/SchoolInfoCard/SchoolInfoCard';
import { ISchoolResponse } from '@/apis/schoolApi';

const schoolData: ISchoolResponse = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  name: 'Springfield Elementary School',
  address: '123 Main Street, Springfield, IL 62701',
  phoneNumber: '+****************',
  registeredNumber: 'REG-2024-001',
  email: '<EMAIL>',
  // ... other fields
};

const handleEdit = (schoolId: string) => {
  console.log('Edit school:', schoolId);
  // Handle edit action (open modal, navigate to edit page, etc.)
};

<SchoolInfoCard
  school={schoolData}
  onEdit={handleEdit}
/>
```

### With Custom Styling

```tsx
<SchoolInfoCard
  school={schoolData}
  onEdit={handleEdit}
  className="border-2 border-blue-200 shadow-lg"
/>
```

### In a Grid Layout

```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {schools.map((school) => (
    <SchoolInfoCard
      key={school.id}
      school={school}
      onEdit={handleEdit}
    />
  ))}
</div>
```

## Displayed Information

The component displays the following school information when available:

1. **School Name** - Displayed prominently as the card title
2. **Address** - Full school address
3. **Contact Email** - School's contact email address
4. **Phone Number** - School's contact phone number
5. **Registered Number** - School's registration number
6. **Administrator** - Admin name and email (if available)

## Responsive Behavior

- **Mobile (< 640px)**: Labels and values stack vertically
- **Tablet & Desktop (≥ 640px)**: Labels and values display side by side
- **Card Width**: Uses `w-full` to fill parent container

## Styling

The component uses DaisyUI classes:

- `card bg-base-100 shadow-sm w-full` - Main card container
- `card-body` - Card content area
- `card-title` - School name styling
- `card-actions justify-end` - Edit button container
- `btn btn-primary btn-sm` - Edit button styling

## Testing

The component includes comprehensive tests covering:

- Rendering of school information
- Handling of optional fields
- Edit button functionality
- Custom className application
- Responsive behavior

Run tests with:
```bash
npm test SchoolInfoCard.test.tsx
```

## Dependencies

- React
- DaisyUI (for styling)
- Lucide React (for edit icon)
- @/components/atoms/Button/Button (project's Button component)
- @/apis/schoolApi (for ISchoolResponse interface)
- @/utils/cn (for className utility)

## Accessibility

- Uses semantic HTML elements
- Proper button labeling for screen readers
- Keyboard navigation support
- Adequate color contrast for text readability

## Browser Support

Compatible with all modern browsers that support:
- ES6+ JavaScript features
- CSS Grid and Flexbox
- React 18+
