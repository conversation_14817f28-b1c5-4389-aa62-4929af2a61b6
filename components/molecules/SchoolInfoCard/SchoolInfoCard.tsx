'use client';

import React from 'react';
import { Edit } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { ISchoolResponse } from '@/apis/schoolApi';
import { cn } from '@/utils/cn';

export interface SchoolInfoCardProps {
  school: ISchoolResponse;
  onEdit: (schoolId: string) => void;
  className?: string;
}

export const SchoolInfoCard: React.FC<SchoolInfoCardProps> = ({
  school,
  onEdit,
  className,
}) => {
  const handleEditClick = () => {
    onEdit(school.id);
  };

  return (
    <div className={cn('card bg-base-100 shadow-sm w-full', className)}>
      <div className="card-body">
        {/* School Name */}
        <h2 className="card-title text-xl font-semibold text-gray-900 mb-4">
          {school.name}
        </h2>

        {/* School Details */}
        <div className="space-y-3">
          {/* Address */}
          {school.address && (
            <div className="flex flex-col sm:flex-row sm:items-start gap-1">
              <span className="text-sm font-medium text-gray-600 min-w-[100px]">
                Address:
              </span>
              <span className="text-sm text-gray-800 flex-1">
                {school.address}
              </span>
            </div>
          )}

          {/* Contact Email */}
          {school.email && (
            <div className="flex flex-col sm:flex-row sm:items-center gap-1">
              <span className="text-sm font-medium text-gray-600 min-w-[100px]">
                Email:
              </span>
              <span className="text-sm text-gray-800 flex-1">
                {school.email}
              </span>
            </div>
          )}

          {/* Phone Number */}
          {school.phoneNumber && (
            <div className="flex flex-col sm:flex-row sm:items-center gap-1">
              <span className="text-sm font-medium text-gray-600 min-w-[100px]">
                Phone:
              </span>
              <span className="text-sm text-gray-800 flex-1">
                {school.phoneNumber}
              </span>
            </div>
          )}

          {/* Registered Number */}
          {school.registeredNumber && (
            <div className="flex flex-col sm:flex-row sm:items-center gap-1">
              <span className="text-sm font-medium text-gray-600 min-w-[100px]">
                Reg. Number:
              </span>
              <span className="text-sm text-gray-800 flex-1">
                {school.registeredNumber}
              </span>
            </div>
          )}

          {/* Admin Information */}
          {school.admin && (
            <div className="flex flex-col sm:flex-row sm:items-start gap-1">
              <span className="text-sm font-medium text-gray-600 min-w-[100px]">
                Administrator:
              </span>
              <div className="text-sm text-gray-800 flex-1">
                <div>{school.admin.name}</div>
                <div className="text-gray-600">{school.admin.email}</div>
              </div>
            </div>
          )}
        </div>

        {/* Card Actions */}
        <div className="card-actions justify-end mt-6">
          <Button
            variant="primary"
            className="btn-sm"
            onClick={handleEditClick}
            iconProps={{
              variant: 'edit',
              size: 16,
            }}
          >
            Edit
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SchoolInfoCard;
