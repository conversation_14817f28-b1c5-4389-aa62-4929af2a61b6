'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { IUserResponse } from '@/apis/userApi';

export interface WizardStep {
  id: number;
  title: string;
  description: string;
}

export interface SignUpWizardState {
  currentStep: number;
  userData: IUserResponse | null;
  isLoading: boolean;
  error: string | null;
  steps: WizardStep[];
}

export interface SignUpWizardContextType {
  state: SignUpWizardState;
  nextStep: () => void;
  previousStep: () => void;
  setUserData: (userData: IUserResponse) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  resetWizard: () => void;
}

const SignUpWizardContext = createContext<SignUpWizardContextType | undefined>(undefined);

const WIZARD_STEPS: WizardStep[] = [
  {
    id: 1,
    title: 'Create Account',
    description: 'Enter your personal information',
  },
  {
    id: 2,
    title: 'Create School',
    description: 'Set up your school details',
  },
  {
    id: 3,
    title: 'Welcome',
    description: 'You\'re all set!',
  },
];

const initialState: SignUpWizardState = {
  currentStep: 1,
  userData: null,
  isLoading: false,
  error: null,
  steps: WIZARD_STEPS,
};

export function SignUpWizardProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<SignUpWizardState>(initialState);

  const nextStep = () => {
    setState(prev => ({
      ...prev,
      currentStep: Math.min(prev.currentStep + 1, WIZARD_STEPS.length),
      error: null,
    }));
  };

  const previousStep = () => {
    setState(prev => ({
      ...prev,
      currentStep: Math.max(prev.currentStep - 1, 1),
      error: null,
    }));
  };

  const setUserData = (userData: IUserResponse) => {
    setState(prev => ({
      ...prev,
      userData,
    }));
  };

  const setLoading = (loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading,
    }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({
      ...prev,
      error,
    }));
  };

  const resetWizard = () => {
    setState(initialState);
  };

  const contextValue: SignUpWizardContextType = {
    state,
    nextStep,
    previousStep,
    setUserData,
    setLoading,
    setError,
    resetWizard,
  };

  return (
    <SignUpWizardContext.Provider value={contextValue}>
      {children}
    </SignUpWizardContext.Provider>
  );
}

export function useSignUpWizard() {
  const context = useContext(SignUpWizardContext);
  if (context === undefined) {
    throw new Error('useSignUpWizard must be used within a SignUpWizardProvider');
  }
  return context;
}
