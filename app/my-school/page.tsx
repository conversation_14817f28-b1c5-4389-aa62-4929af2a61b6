'use client';

import React from 'react';
import { MySchoolDashboard } from '@/components/organisms/MySchoolDashboard';
import DashboardTemplate from '@/components/templates/Dashboard/Dashboard';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { EUserRole } from '@/config/enums/user';

export default function MySchoolPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Default props for DashboardTemplate
  const defaultSidebarItems = [
    { label: "Dashboard", href: "/" },
    { label: "My School", href: "/my-school" }
  ];

  const defaultUserMenuDropdownProps = {
    userName: session?.user?.name || undefined,
    userEmail: session?.user?.email || undefined,
  };

  // Loading state
  if (status === 'loading') {
    return (
      <DashboardTemplate 
        sidebarItems={defaultSidebarItems} 
        userMenuDropdownProps={defaultUserMenuDropdownProps} 
        schoolInfo={null}
      >
        <div className="flex flex-col items-center justify-center min-h-[400px]">
          <Loader2 size={48} className="animate-spin text-blue-600 mb-4" />
          <p className="text-lg font-medium text-gray-700">Loading session...</p>
        </div>
      </DashboardTemplate>
    );
  }

  // Redirect if not authenticated
  if (status === 'unauthenticated') {
    router.push("/auth/sign-in");
    return (
      <DashboardTemplate 
        sidebarItems={defaultSidebarItems} 
        userMenuDropdownProps={defaultUserMenuDropdownProps} 
        schoolInfo={null}
      >
        <p className="text-center">Redirecting to sign-in...</p>
      </DashboardTemplate>
    );
  }

  // Check if user is INDEPENDENT_TEACHER
  if (session?.user?.role !== EUserRole.INDEPENDENT_TEACHER) {
    return (
      <DashboardTemplate 
        sidebarItems={defaultSidebarItems} 
        userMenuDropdownProps={defaultUserMenuDropdownProps} 
        schoolInfo={null}
      >
        <div className="flex flex-col items-center justify-center min-h-[400px]">
          <div className="alert alert-error max-w-md">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Access denied. This page is only available to Independent Teachers.</span>
          </div>
        </div>
      </DashboardTemplate>
    );
  }

  return (
    <DashboardTemplate 
      sidebarItems={[]} // Sidebar items will be populated by layout
      userMenuDropdownProps={defaultUserMenuDropdownProps} 
      schoolInfo={null}
    >
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-2">My School</h1>
          <p className="text-gray-600 mb-4">
            Manage your school information and view school statistics.
          </p>
        </div>

        {/* MySchoolDashboard Component */}
        <MySchoolDashboard />
      </div>
    </DashboardTemplate>
  );
}
