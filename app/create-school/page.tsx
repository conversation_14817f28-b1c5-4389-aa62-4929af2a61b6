'use client';

import React from 'react';
import { IndependentTeacherSchoolForm } from '@/components/organisms/IndependentTeacherSchoolForm';
import DashboardTemplate from '@/components/templates/Dashboard/Dashboard';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Loader2, ArrowLeft } from 'lucide-react';
import { EUserRole } from '@/config/enums/user';
import Link from 'next/link';

export default function CreateSchoolPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Default props for DashboardTemplate
  const defaultSidebarItems = [
    { label: "Dashboard", href: "/" },
    { label: "Create School", href: "/create-school" }
  ];

  const defaultUserMenuDropdownProps = {
    userName: session?.user?.name || undefined,
    userEmail: session?.user?.email || undefined,
  };

  // Loading state
  if (status === 'loading') {
    return (
      <DashboardTemplate 
        sidebarItems={defaultSidebarItems} 
        userMenuDropdownProps={defaultUserMenuDropdownProps} 
        schoolInfo={null}
      >
        <div className="flex flex-col items-center justify-center min-h-[400px]">
          <Loader2 size={48} className="animate-spin text-blue-600 mb-4" />
          <p className="text-lg font-medium text-gray-700">Loading session...</p>
        </div>
      </DashboardTemplate>
    );
  }

  // Redirect if not authenticated
  if (status === 'unauthenticated') {
    router.push("/auth/sign-in");
    return (
      <DashboardTemplate 
        sidebarItems={defaultSidebarItems} 
        userMenuDropdownProps={defaultUserMenuDropdownProps} 
        schoolInfo={null}
      >
        <p className="text-center">Redirecting to sign-in...</p>
      </DashboardTemplate>
    );
  }

  // Check if user is INDEPENDENT_TEACHER
  if (session?.user?.role !== EUserRole.INDEPENDENT_TEACHER) {
    return (
      <DashboardTemplate 
        sidebarItems={defaultSidebarItems} 
        userMenuDropdownProps={defaultUserMenuDropdownProps} 
        schoolInfo={null}
      >
        <div className="flex flex-col items-center justify-center min-h-[400px]">
          <div className="alert alert-error max-w-md">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Access denied. This page is only available to Independent Teachers.</span>
          </div>
        </div>
      </DashboardTemplate>
    );
  }

  return (
    <DashboardTemplate 
      sidebarItems={[]} // Sidebar items will be populated by layout
      userMenuDropdownProps={defaultUserMenuDropdownProps} 
      schoolInfo={null}
    >
      <div className="space-y-6">
        {/* Header Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center gap-4 mb-4">
            <Link 
              href="/" 
              className="btn btn-ghost btn-sm"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Link>
          </div>
          
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Create Your School</h1>
          <p className="text-gray-600 mb-4">
            Set up your educational institution to start managing students, teachers, and academic content.
          </p>
          
          <div className="alert alert-info">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="stroke-current shrink-0 w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
              <h3 className="font-bold">Important Information</h3>
              <div className="text-xs">
                <p>• As an Independent Teacher, you can create and manage only one school.</p>
                <p>• Once created, you'll be able to access your school dashboard and manage school information.</p>
                <p>• All fields marked with * are required.</p>
              </div>
            </div>
          </div>
        </div>

        {/* School Creation Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <IndependentTeacherSchoolForm />
        </div>
      </div>
    </DashboardTemplate>
  );
}
